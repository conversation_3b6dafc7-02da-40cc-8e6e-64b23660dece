<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Proxy - Админ панель</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --sidebar-width: 250px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        /* Стили для формы авторизации */
        .auth-form {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), #34495e);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .auth-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        .auth-card h2 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-color);
            font-weight: 600;
        }
        .auth-card .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: var(--secondary-color);
        }
        .auth-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, var(--secondary-color), #5dade2);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .error {
            color: var(--danger-color);
            margin-top: 15px;
            font-size: 14px;
            font-weight: 500;
        }
        .hidden {
            display: none !important;
        }

        /* Основные стили админки */
        .main-app {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        .main-app.show {
            opacity: 1;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color), #34495e);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar .logo {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
        }

        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--secondary-color), #5dade2);
            color: white;
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #58d68d);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color), #f7dc6f);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color), #ec7063);
        }

        .table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Overlay для мобильного меню */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .mobile-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1001; /* Поверх overlay */
            }

            .sidebar.show {
                transform: translateX(0);
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
            }

            .main-content {
                margin-left: 0;
            }

            /* Убираем overflow для body когда меню открыто */
            body.sidebar-open {
                overflow: hidden;
            }

            /* Улучшенная кнопка меню */
            #sidebarToggle {
                border: 2px solid #dee2e6;
                transition: all 0.2s ease;
            }

            #sidebarToggle:hover {
                background-color: #f8f9fa;
                border-color: #adb5bd;
            }

            #sidebarToggle:focus {
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
        }

        /* Стили для поля ввода ключа администратора */
        .admin-api-input {
            width: 100% !important;
            padding: 12px !important;
            border: 2px solid #e9ecef !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            font-family: monospace !important;
            box-sizing: border-box !important;
            margin: 0 !important;
            transition: border-color 0.3s ease !important;
        }

        .admin-api-input:focus {
            outline: none !important;
            border-color: var(--secondary-color) !important;
            box-shadow: none !important;
        }
    </style>
</head>
<body>
    <!-- Форма авторизации -->
    <div id="authForm" class="auth-form">
        <div class="auth-card">
            <div style="font-size: 48px; color: var(--secondary-color); margin-bottom: 20px;">
                <i class="bi bi-shield-lock"></i>
            </div>
            <h2>Админ панель SMS Proxy</h2>
            <p class="subtitle">
                Введите ключ администратора для доступа к панели управления
            </p>

            <!-- Простая форма без автозаполнения -->
            <div style="margin: 20px 0;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
                    Ключ администратора:
                </label>
                <input
                    type="password"
                    id="adminApiKey"
                    class="form-control admin-api-input"
                    placeholder="Введите ваш ключ администратора"
                    autocomplete="new-password"
                    autocapitalize="off"
                    autocorrect="off"
                    spellcheck="false"
                    tabindex="1"
                >
            </div>

            <button id="loginButton" style="width: 100%; padding: 12px; background: linear-gradient(135deg, var(--secondary-color), #5dade2); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer;" tabindex="2">
                <i class="bi bi-unlock"></i> Войти в панель
            </button>

            <div id="loginError" class="error hidden"></div>
        </div>
    </div>

    <!-- Основное приложение -->
    <div id="mainApp" class="main-app hidden">
        <!-- Боковая панель -->
        <nav class="sidebar">
            <div class="logo">
                <h4><i class="bi bi-phone"></i> SMS Proxy</h4>
                <small>Админ панель</small>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-section="dashboard">
                        <i class="bi bi-speedometer2"></i> Дашборд
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#users" data-section="users">
                        <i class="bi bi-people"></i> Пользователи
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#activations" data-section="activations">
                        <i class="bi bi-phone-vibrate"></i> Активации
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#transactions" data-section="transactions">
                        <i class="bi bi-credit-card"></i> Транзакции
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#services" data-section="services">
                        <i class="bi bi-gear"></i> Сервисы
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#countries" data-section="countries">
                        <i class="bi bi-globe"></i> Страны
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#prices" data-section="prices">
                        <i class="bi bi-currency-dollar"></i> Цены
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings" data-section="settings">
                        <i class="bi bi-sliders"></i> Настройки
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#logs" data-section="logs">
                        <i class="bi bi-journal-text"></i> Логи
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#providers" data-section="providers">
                        <i class="bi bi-hdd-network"></i> Провайдеры
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Overlay для мобильного меню -->
        <div class="mobile-overlay" id="mobileOverlay"></div>

        <!-- Основной контент -->
        <div class="main-content">
            <!-- Верхняя панель -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button class="btn btn-outline-secondary d-lg-none" id="sidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="ms-auto">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> Администратор
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="bi bi-person"></i> Профиль</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="bi bi-box-arrow-right"></i> Выход</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Контент страниц -->
            <div class="container-fluid p-4">
                <!-- Дашборд -->
                <div id="dashboard-section" class="content-section">
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2><i class="bi bi-speedometer2"></i> Дашборд</h2>
                            <p class="text-muted">Обзор системы SMS активации</p>
                        </div>
                    </div>

                    <!-- Статистические карточки -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Всего пользователей</h6>
                                            <h3 id="total-users">-</h3>
                                            <small class="text-muted">Активных: <span id="active-users">-</span></small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-people fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card success">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Активации</h6>
                                            <h3 id="total-activations">-</h3>
                                            <small class="text-muted">За сегодня: <span id="activations-today">-</span></small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-phone-vibrate fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Доходы</h6>
                                            <h3 id="total-revenue">-</h3>
                                            <small class="text-muted">Баланс: $<span id="total-balance">-</span></small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-currency-dollar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card danger">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">Активные</h6>
                                            <h3 id="active-activations">-</h3>
                                            <small class="text-muted">Успех: <span id="success-rate">-</span>%</small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-activity fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Дополнительная статистика -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-graph-up"></i> Статистика провайдеров</h5>
                                </div>
                                <div class="card-body">
                                    <div id="provider-stats" class="loading">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p>Загрузка...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-star"></i> Популярные сервисы</h5>
                                </div>
                                <div class="card-body">
                                    <div id="top-services" class="loading">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p>Загрузка...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Последние активности -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-clock-history"></i> Последние активации</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-activations" class="loading">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p>Загрузка...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-person-plus"></i> Новые пользователи</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-users" class="loading">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p>Загрузка...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Остальные секции будут скрыты по умолчанию -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2><i class="bi bi-people"></i> Управление пользователями</h2>
                            <p class="text-muted">Просмотр и управление пользователями системы</p>
                        </div>
                    </div>
                    <!-- Контент пользователей будет загружен динамически -->
                    <div id="users-content"></div>
                </div>

                <!-- Другие секции аналогично -->
                <div id="activations-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-phone-vibrate"></i> Активации</h2>
                    <div id="activations-content"></div>
                </div>

                <div id="transactions-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-credit-card"></i> Транзакции</h2>
                    <div id="transactions-content"></div>
                </div>

                <div id="services-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-gear"></i> Сервисы</h2>
                    <div id="services-content"></div>
                </div>

                <div id="countries-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-globe"></i> Страны</h2>
                    <div id="countries-content"></div>
                </div>

                <div id="prices-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-currency-dollar"></i> Цены</h2>
                    <div id="prices-content"></div>
                </div>

                <div id="settings-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-sliders"></i> Настройки</h2>
                    <div id="settings-content"></div>
                </div>

                <div id="logs-section" class="content-section" style="display: none;">
                    <h2><i class="bi bi-journal-text"></i> Логи системы</h2>
                    <div id="logs-content"></div>
                </div>

                <!-- Секция управления провайдерами -->
                <div id="providers-section" class="content-section" style="display: none;">
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2><i class="bi bi-hdd-network"></i> Управление провайдерами</h2>
                            <p class="text-muted">Динамическое управление SMS провайдерами разных типов</p>
                        </div>
                    </div>

                    <!-- Кнопки управления -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <button class="btn btn-primary" onclick="adminPanel.showProviderModal()">
                                <i class="bi bi-plus-circle"></i> Добавить провайдер
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="adminPanel.reloadProviders()">
                                <i class="bi bi-arrow-clockwise"></i> Обновить
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-info" onclick="adminPanel.testAllProviders()">
                                <i class="bi bi-shield-check"></i> Тест всех провайдеров
                            </button>
                        </div>
                    </div>

                    <!-- Таблица провайдеров -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-table"></i> Список провайдеров</h5>
                        </div>
                        <div class="card-body">
                            <div id="providers-content" class="loading">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p>Загрузка провайдеров...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Модальные окна -->

    <!-- Модальное окно провайдера -->
    <div class="modal fade" id="providerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-hdd-network"></i> <span id="provider-modal-title">Добавить провайдер</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="providerForm">
                        <!-- Базовые настройки -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="provider-name" class="form-label">Название провайдера *</label>
                                <input type="text" class="form-control" id="provider-name" name="name" required
                                       placeholder="например: provider3">
                                <div class="form-text">Уникальное имя провайдера (только латинские буквы и цифры)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="provider-display-name" class="form-label">Отображаемое имя *</label>
                                <input type="text" class="form-control" id="provider-display-name" name="display_name" required
                                       placeholder="например: Провайдер №3">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="provider-api-url" class="form-label">API URL *</label>
                                <input type="url" class="form-control" id="provider-api-url" name="api_url" required
                                       placeholder="https://api.provider.com/">
                            </div>
                            <div class="col-md-4">
                                <label for="provider-api-format" class="form-label">Тип API *</label>
                                <select class="form-select" id="provider-api-format" name="api_format" required onchange="adminPanel.onApiFormatChange(this.value)">
                                    <option value="">Выберите тип API</option>
                                    <option value="smsactivate_api">SMSActivate совместимый</option>
                                    <option value="firefox_api">Firefox совместимый</option>
                                </select>
                            </div>
                        </div>

                        <!-- Поля для SMSActivate API -->
                        <div id="smsactivate-fields" style="display: none;">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white">
                                    <i class="bi bi-key"></i> Настройки SMSActivate API
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <label for="provider-api-key" class="form-label">API ключ *</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="provider-api-key" name="api_key"
                                                       placeholder="Введите API ключ провайдера">
                                                <button class="btn btn-outline-secondary" type="button" onclick="adminPanel.togglePasswordVisibility('provider-api-key', this)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                            <div class="form-text">API ключ для авторизации (обычно длинная строка символов)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Поля для Firefox API -->
                        <div id="firefox-fields" style="display: none;">
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <i class="bi bi-person-badge"></i> Настройки Firefox API
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="provider-username" class="form-label">Логин *</label>
                                            <input type="text" class="form-control" id="provider-username" name="username"
                                                   placeholder="Логин пользователя">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="provider-password" class="form-label">Пароль *</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="provider-password" name="password"
                                                       placeholder="Пароль пользователя">
                                                <button class="btn btn-outline-secondary" type="button" onclick="adminPanel.togglePasswordVisibility('provider-password', this)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Дополнительные настройки -->
                        <div class="card border-secondary mb-3">
                            <div class="card-header">
                                <i class="bi bi-gear"></i> Дополнительные настройки
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="provider-priority" class="form-label">Приоритет</label>
                                        <input type="number" class="form-control" id="provider-priority" name="priority" value="100" min="1" max="1000">
                                        <div class="form-text">1 = высший приоритет</div>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="provider-timeout" class="form-label">Таймаут (сек)</label>
                                        <input type="number" class="form-control" id="provider-timeout" name="timeout_seconds" value="30" min="5" max="300">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="provider-limit" class="form-label">Лимит запр/мин</label>
                                        <input type="number" class="form-control" id="provider-limit" name="max_requests_per_minute" value="60" min="1" max="1000">
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="provider-active" name="is_active" checked>
                                            <label class="form-check-label" for="provider-active">
                                                <i class="bi bi-toggle-on text-success"></i> Провайдер активен
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="provider-id" name="id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> Отмена
                    </button>
                    <button type="button" class="btn btn-info" onclick="adminPanel.testProviderConnection()">
                        <i class="bi bi-shield-check"></i> Тест API
                    </button>
                    <button type="button" class="btn btn-primary" onclick="adminPanel.saveProvider()">
                        <i class="bi bi-check-circle"></i> Сохранить
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Пользователь</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Форма пользователя -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Переменные для авторизации
        let currentApiKey = '';
        let isAuthenticated = false;

        console.log('Админ панель загружена');

        // Объединенный обработчик DOMContentLoaded (убираем дублирование)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM загружен, проверяем авторизацию...');

            const savedApiKey = localStorage.getItem('admin_api_key');
            console.log('Сохраненный API ключ:', savedApiKey ? 'найден' : 'не найден');

            // Настраиваем обработчики для формы авторизации
            const loginButton = document.getElementById('loginButton');
            const apiKeyField = document.getElementById('adminApiKey');

            // Обработка кнопки входа
            if (loginButton) {
                loginButton.addEventListener('click', async () => {
                    const apiKey = apiKeyField.value.trim();
                    if (apiKey) {
                        console.log('Попытка авторизации с введенным API ключом');
                        await verifyApiKey(apiKey);
                    } else {
                        showError('Введите API ключ администратора');
                    }
                });
            }

            // Обработка Enter в поле ввода
            if (apiKeyField) {
                apiKeyField.addEventListener('keypress', async (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault(); // Предотвращаем отправку формы
                        const apiKey = apiKeyField.value.trim();
                        if (apiKey) {
                            console.log('Попытка авторизации через Enter');
                            await verifyApiKey(apiKey);
                        } else {
                            showError('Введите API ключ администратора');
                        }
                    }
                });

                // Принудительный фокус при клике на поле
                apiKeyField.addEventListener('click', () => {
                    apiKeyField.focus();
                    console.log('Фокус установлен на поле ввода при клике');
                });

                // Очистка ошибок при вводе
                apiKeyField.addEventListener('input', () => {
                    const errorDiv = document.getElementById('loginError');
                    if (errorDiv && !errorDiv.classList.contains('hidden')) {
                        errorDiv.classList.add('hidden');
                    }
                });
            }

            // Проверяем сохраненный ключ или показываем форму
            if (savedApiKey) {
                console.log('Проверяем сохраненный API ключ...');
                verifyApiKey(savedApiKey);
            } else {
                console.log('API ключ не найден, показываем форму авторизации');
                showAuthForm();
            }
        });

        // Показать форму авторизации
        function showAuthForm() {
            console.log('Показываем форму авторизации');
            document.getElementById('authForm').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            isAuthenticated = false;

            // Очищаем поле ввода и устанавливаем фокус
            const apiKeyField = document.getElementById('adminApiKey');
            if (apiKeyField) {
                apiKeyField.value = '';
                // Устанавливаем фокус с небольшой задержкой для стабильности
                setTimeout(() => {
                    apiKeyField.focus();
                    console.log('Фокус установлен на поле ввода');
                }, 100);
            }
        }

        // Показать главное приложение
        function showMainApp() {
            console.log('Показываем главное приложение');
            document.getElementById('authForm').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('show');
            isAuthenticated = true;

            // Инициализируем админ панель если она еще не загружена
            if (typeof adminPanel !== 'undefined') {
                adminPanel.init();
            }
        }

        // Проверка API ключа
        async function verifyApiKey(apiKey) {
            console.log('Проверяем API ключ...');

            // Показываем индикатор загрузки на кнопке
            const loginButton = document.getElementById('loginButton');
            const originalText = loginButton.innerHTML;
            loginButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Проверка...';
            loginButton.disabled = true;

            try {
                const response = await fetch('/admin/verify-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({})
                });

                console.log('Ответ сервера на проверку ключа:', response.status);

                if (response.ok) {
                    console.log('API ключ валидный, показываем админ панель');
                    // API ключ валидный
                    currentApiKey = apiKey;
                    localStorage.setItem('admin_api_key', apiKey);
                    showMainApp();
                } else {
                    console.log('API ключ невалидный');
                    // Очищаем сохраненный ключ если он невалидный
                    localStorage.removeItem('admin_api_key');
                    showAuthForm();
                    showError('Неверный ключ администратора');
                }
            } catch (error) {
                console.error('Ошибка при проверке API ключа:', error);
                localStorage.removeItem('admin_api_key');
                showAuthForm();
                showError('Ошибка проверки ключа администратора');
            } finally {
                // Восстанавливаем кнопку
                loginButton.innerHTML = originalText;
                loginButton.disabled = false;
            }
        }

        // Показать ошибку
        function showError(message) {
            console.log('Показываем ошибку:', message);
            const errorDiv = document.getElementById('loginError');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');

            // Скрыть ошибку через 5 секунд
            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 5000);
        }

        // Функция выхода
        function logout() {
            console.log('Выход из админ панели');
            localStorage.removeItem('admin_api_key');
            currentApiKey = '';
            isAuthenticated = false;
            showAuthForm();
        }

        // Переопределяем функции для работы с авторизацией
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            // Добавляем API ключ к запросам админки
            if (currentApiKey && (url.startsWith('/api/admin') || url.startsWith('/admin/'))) {
                options.headers = {
                    ...options.headers,
                    'X-API-Key': currentApiKey
                };
            }
            return originalFetch(url, options);
        };
    </script>

    <script src="/static/admin/js/admin.js"></script>
</body>
</html>
